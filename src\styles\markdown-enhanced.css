/* Estilos aprimorados para Markdown - Tema Azul <PERSON> */

/* Container principal */
.markdown-content-enhanced {
  @apply text-gray-200 leading-relaxed;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
}

/* Animações suaves */
.markdown-content-enhanced * {
  transition: all 0.2s ease-in-out;
}

/* Títulos com gradientes e efeitos */
.heading-1 {
  position: relative;
  animation: fadeInUp 0.6s ease-out;
}

.heading-1::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-radius: 12px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.heading-1:hover::before {
  opacity: 1;
}

.heading-2 {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.heading-3 {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

/* Parágrafos com espaçamento melhorado */
.paragraph-enhanced {
  text-align: justify;
  hyphens: auto;
}

.paragraph-enhanced:first-of-type {
  margin-top: 0;
}

.paragraph-enhanced:last-of-type {
  margin-bottom: 0;
}

/* Listas estilizadas */
.list-enhanced {
  position: relative;
}

.list-enhanced::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.3), transparent);
  border-radius: 1px;
}

.list-enhanced-ordered {
  counter-reset: enhanced-counter;
  position: relative;
}

.list-enhanced-ordered::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.3), transparent);
  border-radius: 1px;
}

.list-enhanced-ordered .list-item-enhanced {
  counter-increment: enhanced-counter;
}

.list-enhanced-ordered .list-item-enhanced::before {
  content: counter(enhanced-counter);
  position: absolute;
  left: -24px;
  top: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.list-item-enhanced {
  position: relative;
  padding-left: 8px;
  margin-bottom: 8px;
}

.list-item-enhanced:hover {
  @apply text-blue-100;
  transform: translateX(2px);
}

/* Código inline melhorado */
.inline-code {
  position: relative;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-weight: 500;
  letter-spacing: -0.025em;
}

.inline-code::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.inline-code:hover::before {
  opacity: 1;
}

/* Blocos de código aprimorados */
.code-block-wrapper {
  position: relative;
  margin: 1.5rem 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.code-block-header {
  position: relative;
  backdrop-filter: blur(10px);
}

.code-block-header::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(13, 28, 74, 0.9), rgba(27, 46, 118, 0.9));
  z-index: -1;
}

.code-block-content {
  position: relative;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
}

.code-block-content::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  z-index: -1;
}

/* Tabelas aprimoradas */
.table-wrapper {
  position: relative;
  backdrop-filter: blur(10px);
}

.table-wrapper::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05));
  border-radius: inherit;
  z-index: -1;
}

.table-header {
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.table-cell {
  position: relative;
}

.table-cell:hover {
  transform: scale(1.01);
}

/* Blockquotes aprimorados */
.blockquote-enhanced {
  position: relative;
  backdrop-filter: blur(10px);
}

.blockquote-enhanced::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.05));
  border-radius: inherit;
  z-index: -1;
}

/* Links aprimorados */
.link-enhanced {
  position: relative;
}

.link-enhanced::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.link-enhanced:hover::after {
  width: 100%;
}

/* Linha horizontal aprimorada */
.hr-enhanced {
  position: relative;
  animation: fadeIn 0.6s ease-out;
}

/* Animações */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .markdown-content-enhanced {
    font-size: 0.9rem;
    line-height: 1.6;
  }
  
  .heading-1 {
    font-size: 1.75rem;
  }
  
  .heading-2 {
    font-size: 1.5rem;
  }
  
  .heading-3 {
    font-size: 1.25rem;
  }
  
  .code-block-content {
    font-size: 0.8rem;
    padding: 1rem;
  }
  
  .table-wrapper {
    font-size: 0.875rem;
  }
  
  .table-header,
  .table-cell {
    padding: 0.75rem 1rem;
  }
}

/* Melhorias de acessibilidade */
@media (prefers-reduced-motion: reduce) {
  .markdown-content-enhanced *,
  .heading-1,
  .heading-2,
  .heading-3,
  .hr-enhanced {
    animation: none;
    transition: none;
  }
}

/* Modo de alto contraste */
@media (prefers-contrast: high) {
  .markdown-content-enhanced {
    @apply text-white;
  }

  .inline-code {
    @apply bg-blue-800 text-white border-blue-400;
  }

  .code-block-content {
    @apply bg-gray-900 text-white;
  }

  .table-header {
    @apply bg-blue-800 text-white;
  }

  .table-cell {
    @apply text-white;
  }
}

/* Estilos para elementos específicos */
.markdown-content-enhanced img {
  @apply rounded-lg shadow-lg border border-blue-500/30 my-4;
  max-width: 100%;
  height: auto;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.markdown-content-enhanced img:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.2);
}

/* Estilos para kbd (teclas) */
.markdown-content-enhanced kbd {
  @apply bg-gray-700 text-gray-200 px-2 py-1 rounded border border-gray-600 text-xs font-mono;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Estilos para mark (texto destacado) */
.markdown-content-enhanced mark {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.3), rgba(245, 158, 11, 0.3));
  @apply text-yellow-100 px-1 rounded;
  box-shadow: 0 0 8px rgba(251, 191, 36, 0.2);
}

/* Estilos para del (texto riscado) */
.markdown-content-enhanced del {
  @apply text-red-400 opacity-75;
  text-decoration: line-through;
  text-decoration-color: #ef4444;
  text-decoration-thickness: 2px;
}

/* Estilos para ins (texto inserido) */
.markdown-content-enhanced ins {
  @apply text-green-400 bg-green-900/20 px-1 rounded;
  text-decoration: underline;
  text-decoration-color: #22c55e;
  text-decoration-thickness: 2px;
}

/* Estilos para sub e sup */
.markdown-content-enhanced sub,
.markdown-content-enhanced sup {
  @apply text-blue-300 text-xs;
  font-weight: 600;
}

/* Melhorias para seleção de texto */
.markdown-content-enhanced ::selection {
  background: rgba(59, 130, 246, 0.3);
  color: #f1f5f9;
}

.markdown-content-enhanced ::-moz-selection {
  background: rgba(59, 130, 246, 0.3);
  color: #f1f5f9;
}
