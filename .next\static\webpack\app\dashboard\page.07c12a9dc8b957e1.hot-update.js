"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* harmony import */ var _styles_markdown_enhanced_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/markdown-enhanced.css */ \"(app-pages-browser)/./src/styles/markdown-enhanced.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Importar estilos do KaTeX\n\n\n\nconst MarkdownRenderer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { content, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content-enhanced \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                remark_math__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            rehypePlugins: [\n                rehype_katex__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                [\n                    rehype_highlight__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    {\n                        detect: true,\n                        ignoreMissing: true\n                    }\n                ]\n            ],\n            components: {\n                // Customizar renderização de código\n                code (param) {\n                    let { node, className, children, ...props } = param;\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    const language = match ? match[1] : \"\";\n                    const inline = !match;\n                    return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"code-block-wrapper group relative my-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"code-block-header flex items-center justify-between px-4 py-2 bg-gradient-to-r from-rafthor-navy/80 to-rafthor-royal/80 border border-blue-500/30 rounded-t-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium text-blue-300 uppercase tracking-wider\",\n                                        children: language\n                                    }, void 0, false, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigator.clipboard.writeText(String(children)),\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-xs text-blue-400 hover:text-blue-300 px-2 py-1 rounded bg-blue-900/30 hover:bg-blue-800/40\",\n                                        children: \"Copiar\"\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"code-block-content bg-gradient-to-br from-slate-900/95 to-slate-800/95 border border-blue-500/30 border-t-0 rounded-b-lg p-4 overflow-x-auto backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: className,\n                                    ...props,\n                                    children: children\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"inline-code bg-gradient-to-r from-blue-900/40 to-blue-800/40 border border-blue-500/30 px-2 py-1 rounded-md text-sm font-mono text-blue-200 shadow-sm\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de links\n                a (param) {\n                    let { children, href, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"link-enhanced inline-flex items-center gap-1 text-blue-400 hover:text-blue-300 underline decoration-blue-500/50 hover:decoration-blue-300 underline-offset-2 transition-all duration-200 font-medium\",\n                        ...props,\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3 opacity-60\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de tabelas\n                table (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"table-wrapper my-6 overflow-hidden rounded-lg border border-blue-500/30 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border-collapse bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm\",\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                th (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"table-header border-b border-blue-500/30 bg-gradient-to-r from-rafthor-navy/80 to-rafthor-royal/80 px-6 py-4 text-left font-semibold text-white text-sm uppercase tracking-wider\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                td (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"table-cell border-b border-blue-500/20 px-6 py-4 text-gray-200 hover:bg-blue-900/20 transition-colors duration-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de blockquotes\n                blockquote (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"blockquote-enhanced relative border-l-4 border-gradient-to-b from-blue-400 to-blue-600 pl-6 py-4 my-6 bg-gradient-to-r from-blue-900/20 to-transparent rounded-r-lg italic text-blue-100 shadow-lg backdrop-blur-sm\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-2 top-2 text-blue-400/40 text-4xl font-serif\",\n                                children: '\"'\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de listas\n                ul (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-enhanced space-y-2 my-4 pl-6\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                ol (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-enhanced-ordered space-y-2 my-4 pl-6\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                li (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"list-item-enhanced relative pl-2 text-gray-200 leading-relaxed\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute -left-6 top-0 text-blue-400 font-bold\",\n                                children: \"•\"\n                            }, void 0, false, void 0, void 0),\n                            children\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de títulos\n                h1 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"heading-1 text-3xl font-bold mb-6 mt-8 text-transparent bg-gradient-to-r from-blue-300 via-white to-blue-300 bg-clip-text leading-tight\",\n                        ...props,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"relative\",\n                            children: [\n                                children,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-2 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-blue-400 to-transparent\"\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                h2 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"heading-2 text-2xl font-bold mb-4 mt-6 text-transparent bg-gradient-to-r from-blue-200 to-white bg-clip-text\",\n                        ...props,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"relative flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-6 bg-gradient-to-b from-blue-400 to-blue-600 rounded-full\"\n                                }, void 0, false, void 0, void 0),\n                                children\n                            ]\n                        }, void 0, true, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                h3 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"heading-3 text-xl font-semibold mb-3 mt-5 text-blue-100 flex items-center gap-2\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                            }, void 0, false, void 0, void 0),\n                            children\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                h4 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"heading-4 text-lg font-semibold mb-2 mt-4 text-blue-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de parágrafos\n                p (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"paragraph-enhanced mb-4 leading-relaxed text-gray-200 text-base\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de linha horizontal\n                hr (param) {\n                    let { ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hr-enhanced my-8 flex items-center justify-center\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-4 w-2 h-2 bg-blue-400 rounded-full\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent\"\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de texto forte\n                strong (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-bold text-blue-100 bg-gradient-to-r from-blue-900/30 to-blue-800/30 px-1 rounded\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de texto em itálico\n                em (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-blue-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de imagens\n                img (param) {\n                    let { src, alt, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"image-wrapper my-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: src,\n                                alt: alt,\n                                className: \"rounded-lg shadow-lg border border-blue-500/30 max-w-full h-auto mx-auto transition-transform duration-300 hover:scale-105\",\n                                ...props\n                            }, void 0, false, void 0, void 0),\n                            alt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400 mt-2 italic\",\n                                children: alt\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de texto riscado\n                del (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"del\", {\n                        className: \"text-red-400 opacity-75 line-through decoration-red-400 decoration-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de texto inserido\n                ins (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n                        className: \"text-green-400 bg-green-900/20 px-1 rounded underline decoration-green-400 decoration-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de texto destacado\n                mark (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                        className: \"bg-gradient-to-r from-yellow-900/40 to-yellow-800/40 text-yellow-100 px-1 rounded shadow-sm\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de teclas\n                kbd (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                        className: \"bg-gray-700 text-gray-200 px-2 py-1 rounded border border-gray-600 text-xs font-mono shadow-md\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c1, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});