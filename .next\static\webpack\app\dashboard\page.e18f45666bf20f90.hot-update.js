"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Importar estilos do KaTeX\n\n\nconst MarkdownRenderer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { content, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content-enhanced \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                remark_math__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n            ],\n            rehypePlugins: [\n                rehype_katex__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                [\n                    rehype_highlight__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    {\n                        detect: true,\n                        ignoreMissing: true\n                    }\n                ]\n            ],\n            components: {\n                // Customizar renderização de código\n                code (param) {\n                    let { node, className, children, ...props } = param;\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    const language = match ? match[1] : \"\";\n                    const inline = !match;\n                    return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"code-block-wrapper group relative my-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"code-block-header flex items-center justify-between px-4 py-2 bg-gradient-to-r from-rafthor-navy/80 to-rafthor-royal/80 border border-blue-500/30 rounded-t-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium text-blue-300 uppercase tracking-wider\",\n                                        children: language\n                                    }, void 0, false, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigator.clipboard.writeText(String(children)),\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-xs text-blue-400 hover:text-blue-300 px-2 py-1 rounded bg-blue-900/30 hover:bg-blue-800/40\",\n                                        children: \"Copiar\"\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"code-block-content bg-gradient-to-br from-slate-900/95 to-slate-800/95 border border-blue-500/30 border-t-0 rounded-b-lg p-4 overflow-x-auto backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: className,\n                                    ...props,\n                                    children: children\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"inline-code bg-gradient-to-r from-blue-900/40 to-blue-800/40 border border-blue-500/30 px-2 py-1 rounded-md text-sm font-mono text-blue-200 shadow-sm\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de links\n                a (param) {\n                    let { children, href, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"link-enhanced inline-flex items-center gap-1 text-blue-400 hover:text-blue-300 underline decoration-blue-500/50 hover:decoration-blue-300 underline-offset-2 transition-all duration-200 font-medium\",\n                        ...props,\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3 opacity-60\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de tabelas\n                table (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"table-wrapper my-6 overflow-hidden rounded-lg border border-blue-500/30 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border-collapse bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm\",\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                th (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"table-header border-b border-blue-500/30 bg-gradient-to-r from-rafthor-navy/80 to-rafthor-royal/80 px-6 py-4 text-left font-semibold text-white text-sm uppercase tracking-wider\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                td (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"table-cell border-b border-blue-500/20 px-6 py-4 text-gray-200 hover:bg-blue-900/20 transition-colors duration-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de blockquotes\n                blockquote (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"blockquote-enhanced relative border-l-4 border-gradient-to-b from-blue-400 to-blue-600 pl-6 py-4 my-6 bg-gradient-to-r from-blue-900/20 to-transparent rounded-r-lg italic text-blue-100 shadow-lg backdrop-blur-sm\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-2 top-2 text-blue-400/40 text-4xl font-serif\",\n                                children: '\"'\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de listas\n                ul (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-enhanced space-y-2 my-4 pl-6\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                ol (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-enhanced-ordered space-y-2 my-4 pl-6\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                li (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"list-item-enhanced relative pl-2 text-gray-200 leading-relaxed\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute -left-6 top-0 text-blue-400 font-bold\",\n                                children: \"•\"\n                            }, void 0, false, void 0, void 0),\n                            children\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de títulos\n                h1 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"heading-1 text-3xl font-bold mb-6 mt-8 text-transparent bg-gradient-to-r from-blue-300 via-white to-blue-300 bg-clip-text leading-tight\",\n                        ...props,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"relative\",\n                            children: [\n                                children,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-2 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-blue-400 to-transparent\"\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                h2 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"heading-2 text-2xl font-bold mb-4 mt-6 text-transparent bg-gradient-to-r from-blue-200 to-white bg-clip-text\",\n                        ...props,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"relative flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-6 bg-gradient-to-b from-blue-400 to-blue-600 rounded-full\"\n                                }, void 0, false, void 0, void 0),\n                                children\n                            ]\n                        }, void 0, true, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                h3 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"heading-3 text-xl font-semibold mb-3 mt-5 text-blue-100 flex items-center gap-2\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                            }, void 0, false, void 0, void 0),\n                            children\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                h4 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"heading-4 text-lg font-semibold mb-2 mt-4 text-blue-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de parágrafos\n                p (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"paragraph-enhanced mb-4 leading-relaxed text-gray-200 text-base\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de linha horizontal\n                hr (param) {\n                    let { ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hr-enhanced my-8 flex items-center justify-center\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-4 w-2 h-2 bg-blue-400 rounded-full\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent\"\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de texto forte\n                strong (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-bold text-blue-100 bg-gradient-to-r from-blue-900/30 to-blue-800/30 px-1 rounded\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de texto em itálico\n                em (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-blue-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c1, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});