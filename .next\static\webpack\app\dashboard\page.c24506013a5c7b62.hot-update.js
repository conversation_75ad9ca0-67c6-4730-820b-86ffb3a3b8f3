"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Importar estilos do KaTeX\n\n\nconst MarkdownRenderer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { content, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content-enhanced \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                remark_math__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n            ],\n            rehypePlugins: [\n                rehype_katex__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                [\n                    rehype_highlight__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    {\n                        detect: true,\n                        ignoreMissing: true\n                    }\n                ]\n            ],\n            components: {\n                // Customizar renderização de código\n                code (param) {\n                    let { node, className, children, ...props } = param;\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    const language = match ? match[1] : \"\";\n                    const inline = !match;\n                    return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"code-block-wrapper group relative my-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"code-block-header flex items-center justify-between px-4 py-2 bg-gradient-to-r from-rafthor-navy/80 to-rafthor-royal/80 border border-blue-500/30 rounded-t-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium text-blue-300 uppercase tracking-wider\",\n                                        children: language\n                                    }, void 0, false, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigator.clipboard.writeText(String(children)),\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-xs text-blue-400 hover:text-blue-300 px-2 py-1 rounded bg-blue-900/30 hover:bg-blue-800/40\",\n                                        children: \"Copiar\"\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"code-block-content bg-gradient-to-br from-slate-900/95 to-slate-800/95 border border-blue-500/30 border-t-0 rounded-b-lg p-4 overflow-x-auto backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: className,\n                                    ...props,\n                                    children: children\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"inline-code bg-gradient-to-r from-blue-900/40 to-blue-800/40 border border-blue-500/30 px-2 py-1 rounded-md text-sm font-mono text-blue-200 shadow-sm\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de links\n                a (param) {\n                    let { children, href, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"link-enhanced inline-flex items-center gap-1 text-blue-400 hover:text-blue-300 underline decoration-blue-500/50 hover:decoration-blue-300 underline-offset-2 transition-all duration-200 font-medium\",\n                        ...props,\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3 opacity-60\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de tabelas\n                table (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"table-wrapper my-6 overflow-hidden rounded-lg border border-blue-500/30 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border-collapse bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm\",\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                th (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"table-header border-b border-blue-500/30 bg-gradient-to-r from-rafthor-navy/80 to-rafthor-royal/80 px-6 py-4 text-left font-semibold text-white text-sm uppercase tracking-wider\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                td (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"table-cell border-b border-blue-500/20 px-6 py-4 text-gray-200 hover:bg-blue-900/20 transition-colors duration-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de blockquotes\n                blockquote (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"blockquote-enhanced relative border-l-4 border-gradient-to-b from-blue-400 to-blue-600 pl-6 py-4 my-6 bg-gradient-to-r from-blue-900/20 to-transparent rounded-r-lg italic text-blue-100 shadow-lg backdrop-blur-sm\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-2 top-2 text-blue-400/40 text-4xl font-serif\",\n                                children: '\"'\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de listas\n                ul (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-enhanced space-y-2 my-4 pl-6\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                ol (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-enhanced-ordered space-y-2 my-4 pl-6\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                li (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"list-item-enhanced relative pl-2 text-gray-200 leading-relaxed\",\n                        ...props,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute -left-6 top-0 text-blue-400 font-bold\",\n                                children: \"•\"\n                            }, void 0, false, void 0, void 0),\n                            children\n                        ]\n                    }, void 0, true, void 0, void 0);\n                },\n                // Customizar renderização de títulos\n                h1 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-4 mt-6 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                h2 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-3 mt-5 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                h3 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold mb-2 mt-4 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de parágrafos\n                p (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-3 leading-relaxed text-gray-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de linha horizontal\n                hr (param) {\n                    let { ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-gray-600 my-6\",\n                        ...props\n                    }, void 0, false, void 0, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c1, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});